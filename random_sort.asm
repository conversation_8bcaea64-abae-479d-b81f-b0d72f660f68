# 系统初始化
    li   sp, 0x10000          # 初始化栈指针 (0x10000)
    lui  s1, 0xFFFFF          # 设置I/O基地址 (0xFFFFF000)
    addi s0, sp, -4           # 设置栈边界 (sp - 4)
    addi s2, zero, 0          # 初始化LFSR状态寄存器

# 阶段0：显示计时器值
# 功能：循环读取32位计时器值并显示
STAGE0:
    lw   t0, 0x20(s1)         # 读取计时器值 (I/O地址 0x20)
    sw   t0, 0x00(s1)         # 显示到数码管 (I/O地址 0x00)
    
    lw   a0, 0x70(s1)         # 读取开关状态 (I/O地址 0x70)
    andi a0, a0, 0x3          # 提取SW[1:0]
    addi a1, zero, 1          # 设置目标状态：阶段1
    bne  a0, a1, STAGE0       # 循环直到SW[1:0] == 1

# 阶段1：显示随机数种子
# 功能：将计时器值作为种子显示
STAGE1:
    sw   t0, 0x00(s1)         # 显示种子值（阶段0最后读取的计时器值）
    addi s2, t0, 0            # 初始化LFSR状态为种子值

    lw   a0, 0x70(s1)         # 读取开关状态
    andi a0, a0, 0x3          # 提取SW[1:0]
    addi a1, zero, 2          # 设置目标状态：阶段2
    bne  a0, a1, STAGE1       # 循环直到SW[1:0] == 2

# 阶段2：生成并显示随机数数组
# 功能：使用种子生成8个4位随机数，并显示32位组合值
    addi sp, sp, -32          # 栈上分配32字节空间(8x4字节)
STAGE2_LOOP:
# 使用lab1中的LFSR算法生成8个4位随机数
    addi t6, zero, 0          # 循环计数器
    addi t0, zero, 0          # 用于组合显示的32位值

generate_8_numbers:
    jal  ra, lfsr_next        # 调用LFSR函数生成随机数
    andi a2, a0, 15           # 取4位 (0-15)

    # 存储到栈
    slli t1, t6, 2            # 计算偏移量 (i*4)
    add  t2, sp, t1           # 计算地址
    sw   a2, 0(t2)            # 存储随机数

    # 组合到显示值中
    slli t1, t6, 2            # 每个4位元素占用i*4位
    sll  a3, a2, t1           # 左移到正确位置
    or   t0, t0, a3           # 合并到结果

    addi t6, t6, 1            # 计数器+1
    addi t7, zero, 8
    blt  t6, t7, generate_8_numbers  # 继续生成直到8个数字

#显示生成的32位随机数组合
    sw   t0, 0x00(s1)         # 输出到数码管
    
#检查状态转换
    lw   a0, 0x70(s1)         # 读取开关状态
    andi a0, a0, 0x3          # 提取SW[1:0]
    addi a1, zero, 3          # 设置目标状态：阶段3
    bne  a0, a1, STAGE2_LOOP  # 循环直到SW[1:0] == 3


# 阶段3：排序随机数数组
# 功能：对栈中的8个4位数字进行冒泡排序
    addi s0, sp, 32           # 设置数组边界(sp + 32)
    addi t1, zero, 8          # 外循环计数器(元素数量=8)
OUTER_LOOP:
    addi t2, sp, 0            # 内循环指针(数组起始)
INNER_LOOP:
    lw   s4, 0(t2)            # 加载当前元素
    lw   s5, 4(t2)            # 加载下一元素
    
#比较并交换元素
    bge  s4, s5, NO_SWAP      # 若顺序正确则跳过交换
    sw   s4, 4(t2)            # 交换元素位置
    sw   s5, 0(t2)            # 
NO_SWAP:
    addi t2, t2, 4            # 移动到下一元素
    blt  t2, s0, INNER_LOOP   # 内循环直到数组结束
    
    addi t1, t1, -1           # 外循环计数减1
    bgt  t1, zero, OUTER_LOOP # 继续外循环
    
#排序完成信号
    addi t3, zero, 1          # 
    sw   t3, 0x60(s1)         # 点亮LED[0] (I/O地址 0x60)
    
#等待状态转换
STAGE3_WAIT:
    lw   a0, 0x70(s1)         # 读取开关状态
    andi a0, a0, 0x3          # 提取SW[1:0]
    bne  a0, zero, STAGE3_WAIT # 循环直到SW[1:0] == 0

# 阶段4：显示排序结果
# 功能：组合排序后的8个4位数字并显示

#从栈中加载排序后的数字
    lw   a2, 0(sp)            # 加载数字0
    lw   a3, 4(sp)            # 加载数字1
    lw   a4, 8(sp)            # 加载数字2
    lw   a5, 12(sp)           # 加载数字3
    lw   a6, 16(sp)           # 加载数字4
    lw   a7, 20(sp)           # 加载数字5
    lw   t4, 24(sp)           # 加载数字6
    lw   t5, 28(sp)           # 加载数字7
    
#组合为32位数
    add  t0, zero, zero       # 初始化结果寄存器
    add  t0, t0, a2           # 添加数字0
    slli t0, t0, 4            # 左移4位
    add  t0, t0, a3           # 添加数字1
    slli t0, t0, 4            # 
    add  t0, t0, a4           # 添加数字2
    slli t0, t0, 4            # 
    add  t0, t0, a5           # 添加数字3
    slli t0, t0, 4            # 
    add  t0, t0, a6           # 添加数字4
    slli t0, t0, 4            # 
    add  t0, t0, a7           # 添加数字5
    slli t0, t0, 4            #
    add  t0, t0, t4           # 添加数字6
    slli t0, t0, 4            #
    add  t0, t0, t5           # 添加数字7
    
    addi sp, sp, 32           # 释放栈空间
    sw   t0, 0x00(s1)         # 显示排序结果
    
END:
    jal  zero, END            # 永久循环保持显示状态

# 线性反馈移位寄存器 (LFSR) 函数
# 使用16位LFSR，多项式: x^16 + x^14 + x^13 + x^11 + 1
# 输入：s2 = LFSR状态
# 输出：a0 = 生成的随机数
lfsr_next:
    # 计算反馈位
    srli t0, s2, 0          # bit 0
    srli t1, s2, 2          # bit 2
    srli t2, s2, 3          # bit 3
    srli t3, s2, 5          # bit 5

    andi t0, t0, 1
    andi t1, t1, 1
    andi t2, t2, 1
    andi t3, t3, 1

    xor t0, t0, t1
    xor t0, t0, t2
    xor t0, t0, t3          # 反馈位

    # 左移并插入反馈位
    slli s2, s2, 1
    or s2, s2, t0
    # 保持16位 - 使用多条指令
    lui t4, 0x1
    addi t4, t4, -1         # t4 = 0xFFFF
    and s2, s2, t4

    # 防止全零状态
    bne s2, zero, lfsr_done
    addi s2, zero, 1        # 如果为0，设为1

lfsr_done:
    addi a0, s2, 0          # 返回当前LFSR值
    jalr zero, ra, 0